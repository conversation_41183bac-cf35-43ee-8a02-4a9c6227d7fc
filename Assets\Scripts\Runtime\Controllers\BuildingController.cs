using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using QFramework;
using QHLC.Models;
using QHLC.Events;
using QHLC.Views;
using F8Framework.Core;
using QHLC.Utilities;

namespace QHLC.Controllers
{
    /// <summary>
    /// 建筑控制器，管理建筑的显示和交互
    /// </summary>
    public class BuildingController : MonoBeh<PERSON>our, IController
    {
        [SerializeField] private List<BuildingView> buildingViewsList = new List<BuildingView>();

        private Dictionary<int, BuildingView> buildingViews = new Dictionary<int, BuildingView>();

        /// <summary>
        /// 获取所有建筑视图
        /// </summary>
        /// <returns>建筑ID到建筑视图的映射</returns>
        public Dictionary<int, BuildingView> GetBuildingViews()
        {
            return buildingViews;
        }

        public IArchitecture GetArchitecture()
        {
            return QHLCArchitecture.Interface;
        }

        private void Start()
        {
            // 注册事件
            this.RegisterEvent<BuildingCreatedEvent>(OnBuildingCreated);
            this.RegisterEvent<BuildingInteractionEvent>(OnBuildingInteraction);
            this.RegisterEvent<RewardCollectedEvent>(OnRewardCollected);

            // 初始化建筑视图
            InitializeBuildingViews();
        }

        private void OnDestroy()
        {
            // 注销事件
            this.UnRegisterEvent<BuildingCreatedEvent>(OnBuildingCreated);
            this.UnRegisterEvent<BuildingInteractionEvent>(OnBuildingInteraction);
            this.UnRegisterEvent<RewardCollectedEvent>(OnRewardCollected);
        }

        /// <summary>
        /// 初始化建筑视图
        /// </summary>
        private void InitializeBuildingViews()
        {
            try
            {
                var buildingModel = this.GetModel<BuildingModel>();
                if (buildingModel == null)
                {
                    ModuleLogManager.LogError("BuildingController: 无法获取BuildingModel");
                    return;
                }

                var buildings = buildingModel.GetAllBuildings();
                if (buildings == null || buildings.Count == 0)
                {
                    ModuleLogManager.LogWarning("BuildingController: 没有建筑数据需要初始化");
                    return;
                }

                // 检查是否有足够的BuildingView组件
                if (buildingViewsList.Count < buildings.Count)
                {
                    ModuleLogManager.LogWarning($"BuildingController: 场景中的BuildingView组件数量({buildingViewsList.Count})少于建筑数量({buildings.Count})");
                }

                // 清空字典
                buildingViews.Clear();

                // 初始化每个建筑视图
                int successCount = 0;
                for (int i = 0; i < buildings.Count && i < buildingViewsList.Count; i++)
                {
                    var building = buildings[i];
                    var buildingView = buildingViewsList[i];

                    if (buildingView != null && building != null)
                    {
                        try
                        {
                            InitializeBuildingView(buildingView, building);
                            buildingViews[building.Id] = buildingView;
                            successCount++;
                        }
                        catch (System.Exception ex)
                        {
                            ModuleLogManager.LogError($"BuildingController: 初始化建筑视图 {i} 失败: {ex.Message}");
                        }
                    }
                    else
                    {
                        ModuleLogManager.LogError($"BuildingController: 第{i}个BuildingView或Building为空");
                    }
                }

                ModuleLogManager.Log($"BuildingController: 成功初始化了 {successCount}/{buildings.Count} 个建筑视图");
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogError($"BuildingController: 初始化建筑视图时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化单个建筑视图
        /// </summary>
        /// <param name="buildingView">建筑视图组件</param>
        /// <param name="building">建筑数据</param>
        private void InitializeBuildingView(BuildingView buildingView, Building building)
        {
            buildingView.Initialize(building);

            // 获取建筑的奖励
            var rewardModel = this.GetModel<RewardModel>();
            var rewards = rewardModel.GetRewardsByBuildingId(building.Id);

            // 创建奖励视图
            foreach (var reward in rewards)
            {
                buildingView.AddRewardView(reward);
            }

            // 设置建筑位置
            var submarineModel = this.GetModel<SubmarineModel>();
            var pathItems = submarineModel.GetPathItems();
            var stationPathItem = pathItems.Find(p => p.stationIndex == building.StationIndex);

            if (stationPathItem != null)
            {
                // 优化：直接使用transform as RectTransform避免GetComponent分配
                RectTransform buildingRect = buildingView.transform as RectTransform;
                if (buildingRect != null)
                {
                    // 使用PathItem的Position属性而不是pointTransform.anchoredPosition
                    buildingRect.anchoredPosition = stationPathItem.Position;
                    ModuleLogManager.Log($"BuildingController: 设置建筑 {building.Id} 位置为 {buildingRect.anchoredPosition}");
                }
                else
                {
                    ModuleLogManager.LogError("BuildingController: BuildingView的Transform不是RectTransform类型");
                }
            }
            else
            {
                ModuleLogManager.LogWarning($"BuildingController: 未找到站点索引为 {building.StationIndex} 的路径点");
            }
        }

        /// <summary>
        /// 处理建筑创建事件
        /// </summary>
        /// <param name="e">事件数据</param>
        private void OnBuildingCreated(BuildingCreatedEvent e)
        {
            // 查找未使用的BuildingView
            BuildingView unusedBuildingView = null;
            foreach (var buildingView in buildingViewsList)
            {
                if (!buildingViews.ContainsValue(buildingView))
                {
                    unusedBuildingView = buildingView;
                    break;
                }
            }

            if (unusedBuildingView != null)
            {
                InitializeBuildingView(unusedBuildingView, e.Building);
                buildingViews[e.Building.Id] = unusedBuildingView;
                ModuleLogManager.Log($"BuildingController: 使用场景中的BuildingView初始化建筑 {e.Building.Id}");
            }
            else
            {
                ModuleLogManager.LogWarning($"BuildingController: 没有可用的BuildingView组件来初始化建筑 {e.Building.Id}");
            }
        }

        /// <summary>
        /// 处理建筑交互事件
        /// </summary>
        /// <param name="e">事件数据</param>
        private void OnBuildingInteraction(BuildingInteractionEvent e)
        {
            try
            {
                if (e?.Building == null)
                {
                    ModuleLogManager.LogError("BuildingController: 建筑交互事件数据无效");
                    return;
                }

                if (buildingViews.TryGetValue(e.Building.Id, out BuildingView buildingView))
                {
                    if (e.Rewards != null && e.Rewards.Count > 0)
                    {
                        // 高亮可收集的奖励
                        foreach (var reward in e.Rewards)
                        {
                            if (reward != null)
                            {
                                buildingView.HighlightReward(reward.Id);

                                // 自动收集奖励
                                this.SendEvent(new CollectRewardEvent { Reward = reward });
                            }
                        }

                        ModuleLogManager.Log($"BuildingController: 激活并自动收集建筑 {e.Building.Id} 的 {e.Rewards.Count} 个奖励");
                    }
                    else
                    {
                        ModuleLogManager.LogWarning($"BuildingController: 建筑 {e.Building.Id} 没有可收集的奖励");
                    }
                }
                else
                {
                    ModuleLogManager.LogError($"BuildingController: 未找到建筑 {e.Building.Id} 对应的视图");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogError($"BuildingController: 处理建筑交互事件时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理奖励收集完成事件
        /// </summary>
        /// <param name="e">事件数据</param>
        private void OnRewardCollected(RewardCollectedEvent e)
        {
            try
            {
                if (e?.Reward == null)
                {
                    ModuleLogManager.LogError("BuildingController: 奖励收集事件数据无效");
                    return;
                }

                var rewardModel = this.GetModel<RewardModel>();
                var buildingModel = this.GetModel<BuildingModel>();

                if (rewardModel == null || buildingModel == null)
                {
                    ModuleLogManager.LogError("BuildingController: 无法获取RewardModel或BuildingModel");
                    return;
                }

                var currentBuilding = buildingModel.CurrentActiveBuilding.Value;

                if (currentBuilding != null && buildingViews.TryGetValue(currentBuilding.Id, out BuildingView buildingView))
                {
                    buildingView.PlayRewardCollectedAnimation(e.Reward.Id);

                    // 检查是否还有可收集的奖励
                    var collectableRewards = rewardModel.CurrentCollectableRewards.Value;
                    if (collectableRewards == null || collectableRewards.Count == 0)
                    {
                        buildingModel.SetCurrentActiveBuilding(null);
                        ModuleLogManager.Log($"BuildingController: 建筑 {currentBuilding.Id} 的所有奖励已收集");
                    }
                    else
                    {
                        ModuleLogManager.Log($"BuildingController: 建筑 {currentBuilding.Id} 还有 {collectableRewards.Count} 个奖励可收集");
                    }
                }
                else
                {
                    ModuleLogManager.LogWarning($"BuildingController: 当前没有激活的建筑或找不到对应的视图");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogError($"BuildingController: 处理奖励收集事件时发生异常: {ex.Message}");
            }
        }
    }
}
